package com.example.chatbot.Services;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

@Service
public class ChatService {
    private final ChatClient chatClient;
    private final VectorStore vectorStore;

    // Lưu trữ lịch sử hội thoại cho mỗi người dùng
    // Key: userId, Value: List of messages (Map<String, String> representing role and content)
    private Map<String, List<Map<String, String>>> conversationHistory = new ConcurrentHashMap<>();
    private static final int MAX_HISTORY_MESSAGES = 10; // Giớ<PERSON> hạn 10 tin nhắn trong lịch sử

    public ChatService(ChatClient.Builder chatClientBuilder, VectorStore vectorStore) {
        this.chatClient = chatClientBuilder.build();
        this.vectorStore = vectorStore;
    }

    public String processMessage(String userId, String userQuery) {
        ChatResponse response1 = chatClient.prompt(userQuery).call().chatResponse();
        String aiResponse1 = "";
        if (response1 != null && response1.getResult() != null && response1.getResult().getOutput() != null) {
            aiResponse1 = response1.getResult().getOutput().getText();
        }

        return aiResponse1;
    }

    // Phương thức để xóa lịch sử hội thoại (ví dụ khi phiên kết thúc)
    public void clearConversationHistory(String userId) {
        conversationHistory.remove(userId);
    }
}
