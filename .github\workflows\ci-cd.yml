# Tên của workflow
name: Build and Deploy Spring Boot App

# Kích hoạt workflow khi có push lên nhánh 'main'
on:
  push:
    branches: [ "main" ]

# Các công việc sẽ được thực thi
jobs:
  #################
  #   BUILD JOB   #
  #################
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      # Bước 1: Checkout code
      - name: Checkout Code
        uses: actions/checkout@v4

      # Bước 2: Cài đặt JDK 21 (Amazon Corretto)
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'corretto'
          cache: 'maven'

      # Bước 3: Cache các gói phụ thuộc của Maven
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-

      # Bước 4: Build project với Maven
      - name: Build with Maven
        run: mvn -B package -DskipTests=true --file pom.xml

      # Bước 5: Upload file JAR đã build làm artifact
      # Artifact này sẽ được job 'deploy' sử dụng
      - name: Upload JAR artifact
        uses: actions/upload-artifact@v4
        with:
          name: spring-boot-jar # Tên của artifact
          path: target/*.jar     # Đường dẫn đến file cần upload

  ###################
  #   DEPLOY JOB    #
  ###################
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Download JAR artifact
        uses: actions/download-artifact@v4
        with:
          name: spring-boot-jar

      - name: Rename JAR file
        run: mv *.jar application.jar # Đổi tên file để thống nhất

      - name: Copy jar to EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "application.jar"
          target: "~/app"

      - name: Restart Application
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            sudo systemctl restart my-spring-app